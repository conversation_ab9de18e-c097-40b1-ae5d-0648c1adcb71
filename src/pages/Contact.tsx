
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import Seo from '@/components/Seo';
import { useLanguage } from '@/contexts/LanguageContext';

const Contact: React.FC = () => {
  const { t } = useLanguage();
  const [isContactFormOpen, setIsContactFormOpen] = useState(true);

  return (
    <div className="min-h-screen flex flex-col bg-white dark:bg-gray-900">
      <Seo 
        title="Contact Us"
        description="Get in touch with Krushal First Mile for any questions about our AI-powered agriculture and dairy farming solutions."
        canonicalUrl="https://krushal.ai/contact"
        keywords="contact Krushal, agriculture support, dairy farming help, get in touch"
      />
      
      <Navbar />
      
      <section className="pt-28 md:pt-32 pb-16 px-6 flex-grow">
        <div className="container mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-2xl mx-auto"
          >
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-krushal-darkPurple dark:text-white mb-4">
                {t('footer.contactus')}
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                {t('footer.description')}
              </p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
              <ContactForm onClose={() => setIsContactFormOpen(false)} />
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;
