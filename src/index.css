
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
 
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
 
    --primary: 222.1 83.2% 9.9%;
    --primary-foreground: 210 40% 98%;
 
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
 
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
 
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
 
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
 
    --ring: 222.1 83.2% 9.9%;
 
    --radius: 0.5rem;

    --sidebar-background: 240 4.8% 95.9%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 222.1 83.2% 9.9%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 222.1 83.2% 9.9%;
  }
 
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
 
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
 
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
 
    --primary: 210 40% 98%;
    --primary-foreground: 222.1 83.2% 9.9%;
 
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
 
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
 
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
 
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
 
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
 
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 40% 98%;
    --sidebar-primary-foreground: 222.1 83.2% 9.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 212.7 26.8% 83.9%;
  }
}

@layer utilities {
  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }
  
  .touch-callout-none {
    -webkit-touch-callout: none;
  }
}

.agent-card {
  @apply rounded-lg shadow-md overflow-hidden;
}

.agent-card-image {
  @apply w-full h-48 object-cover;
}

.agent-card-content {
  @apply p-4;
}

.agent-card-title {
  @apply text-xl font-semibold mb-2;
}

.agent-card-description {
  @apply text-base text-gray-700;
}

/* Enterprise-scale Mobile Optimization */
@media (max-width: 640px) {
  body {
    @apply text-base;
  }
  
  h1 {
    @apply text-2xl font-bold tracking-tight;
  }
  
  h2 {
    @apply text-xl font-bold tracking-tight;
  }
  
  h3 {
    @apply text-lg font-bold tracking-tight;
  }
  
  .card-title {
    @apply text-lg font-semibold;
  }
  
  .card-description {
    @apply text-sm leading-relaxed;
  }
  
  .agent-card-title {
    @apply text-lg;
  }
  
  .category-title {
    @apply text-xl font-bold;
  }
  
  .category-description {
    @apply text-sm leading-relaxed;
  }
  
  .explore-button {
    @apply text-sm py-2 px-4;
  }
  
  .category-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-5;
  }
  
  /* Enhanced mobile cards with improved text size */
  .mobile-card {
    @apply p-4 rounded-xl shadow-md border border-gray-200 dark:border-gray-700;
    @apply flex flex-col gap-3;
  }
  
  .mobile-card-title {
    @apply text-xl font-semibold text-krushal-darkPurple dark:text-white;
  }
  
  .mobile-card-description {
    @apply text-base text-gray-600 dark:text-gray-300 leading-relaxed;
  }
  
  .mobile-card-action {
    @apply text-sm font-medium text-krushal-purple dark:text-krushal-lightPurple;
  }
}

/* Category Card Enhancements */
.category-card-mobile {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg;
  @apply border border-gray-200 dark:border-gray-700;
  @apply p-4 sm:p-5;
  @apply transition-all duration-300 hover:shadow-xl;
}

/* Mobile-friendly category title */
.category-card-mobile-title {
  @apply text-base sm:text-lg font-semibold text-krushal-darkPurple dark:text-white;
  @apply truncate mb-1;
}

/* Agent Card Enhancements with improved text size */
.agent-card-mobile {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden;
  @apply border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-300 hover:shadow-xl;
}

.agent-card-mobile-content {
  @apply p-4 space-y-3;
}

.agent-card-mobile-title {
  @apply text-xl font-semibold text-krushal-darkPurple dark:text-white;
}

.agent-card-mobile-description {
  @apply text-base text-gray-600 dark:text-gray-300 leading-relaxed line-clamp-3;
}

.agent-card-mobile-category {
  @apply text-xs font-medium px-2 py-1 rounded-full bg-krushal-purple/10 text-krushal-purple dark:bg-krushal-purple/20 dark:text-krushal-lightPurple;
}

.agent-card-mobile-action {
  @apply text-base font-medium text-krushal-purple dark:text-krushal-lightPurple flex items-center gap-1;
}
