
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Index from './pages/Index';
import Agents from './pages/Agents';
import NotFound from './pages/NotFound';
import About from './pages/About';
import Contact from './pages/Contact';
import SubmitAgent from './pages/SubmitAgent';
import { LanguageProvider } from './contexts/LanguageContext';
import { initializeAnalytics } from './hooks/use-analytics';
import { useEffect } from 'react';

function App() {
  // Initialize analytics when the app first loads
  useEffect(() => {
    initializeAnalytics();
  }, []);

  return (
    <LanguageProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/agents" element={<Agents />} />
          <Route path="/about" element={<About />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/submit-agent" element={<SubmitAgent />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
    </LanguageProvider>
  );
}

export default App;
